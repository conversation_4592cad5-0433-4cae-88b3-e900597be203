import { NextRequest, NextResponse } from 'next/server';
import { getToken } from 'next-auth/jwt';

// Define protected routes that require authentication
const protectedRoutes = [
  '/dashboard',
  '/profile',
  '/assessment',
  '/forum',
  '/freedom-fund',
  '/progress',
  '/recommendations',
  '/interview-practice',
  '/resume-builder',
  '/tools'
];

// Define admin routes that require admin authentication
const adminRoutes = ['/admin', '/audit'];

// Define API routes that require authentication
const protectedApiRoutes = [
  '/api/assessment',
  '/api/profile',
  '/api/freedom-fund',
  '/api/learning-progress',
  '/api/personalized-resources',
  '/api/progress-tracker',
  '/api/recommendations',
  '/api/resource-ratings',
  '/api/interview-practice',
  '/api/resume-builder',
  '/api/tools',
  '/api/audit'
];

// Define admin API routes that require admin authentication
const adminApiRoutes = ['/api/admin'];

// Define public API routes that don't require authentication
const publicApiRoutes = [
  '/api/auth',
  '/api/signup',
  '/api/career-paths',
  '/api/learning-resources',
  '/api/contact',
  '/api/csrf-token'
];

// Rate limiting store (use Redis in production)
const rateLimitStore = new Map<string, { count: number; resetTime: number }>();

function getClientIP(request: NextRequest): string {
  const forwarded = request.headers.get('x-forwarded-for');
  if (forwarded) {
    return forwarded.split(',')[0]?.trim() || '127.0.0.1';
  }

  const realIP = request.headers.get('x-real-ip');
  if (realIP) {
    return realIP;
  }

  return (request as any).ip || 'unknown';
}

function isRateLimited(
  request: NextRequest,
  windowMs: number = 15 * 60 * 1000,
  maxRequests: number = 100
): boolean {
  const clientIP = getClientIP(request);
  const now = Date.now();
  const windowStart = now - windowMs;

  // Clean up old entries
  Array.from(rateLimitStore.entries()).forEach(([key, value]) => {
    if (value.resetTime < windowStart) {
      rateLimitStore.delete(key);
    }
  });

  // Get or create entry for this IP
  const entry = rateLimitStore.get(clientIP) || { count: 0, resetTime: now + windowMs };

  // Reset if window has expired
  if (entry.resetTime < now) {
    entry.count = 0;
    entry.resetTime = now + windowMs;
  }

  // Increment count
  entry.count++;
  rateLimitStore.set(clientIP, entry);

  return entry.count > maxRequests;
}

function addSecurityHeaders(response: NextResponse): NextResponse {
  // Add comprehensive security headers
  response.headers.set('X-Frame-Options', 'DENY');
  response.headers.set('X-Content-Type-Options', 'nosniff');
  response.headers.set('Referrer-Policy', 'strict-origin-when-cross-origin');
  response.headers.set('X-XSS-Protection', '1; mode=block');
  response.headers.set('Permissions-Policy', 'camera=(), microphone=(), geolocation=()');

  // Generate nonce for inline scripts
  const nonce = crypto.randomUUID();

  // Enhanced Content Security Policy - Removed unsafe-inline for better security
  const csp = [
    "default-src 'self'",
    `script-src 'self' 'nonce-${nonce}' 'unsafe-eval' https://vercel.live https://va.vercel-scripts.com https://browser.sentry-cdn.com`,
    "style-src 'self' 'unsafe-inline' https://fonts.googleapis.com",
    "img-src 'self' data: https: blob:",
    "font-src 'self' data: https://fonts.gstatic.com",
    "connect-src 'self' https://vercel.live https://vitals.vercel-insights.com https://*.sentry.io",
    "worker-src 'self' blob:",
    "child-src 'self' blob:",
    "frame-ancestors 'none'",
    "base-uri 'self'",
    "form-action 'self'"
  ].join('; ');

  response.headers.set('Content-Security-Policy', csp);
  response.headers.set('X-Nonce', nonce);

  // Add HSTS header for HTTPS (always set for security testing)
  response.headers.set('Strict-Transport-Security', 'max-age=31536000; includeSubDomains; preload');

  // Additional security headers
  response.headers.set('Cross-Origin-Embedder-Policy', 'require-corp');
  response.headers.set('Cross-Origin-Opener-Policy', 'same-origin');
  response.headers.set('Cross-Origin-Resource-Policy', 'same-origin');

  return response;
}

export async function middleware(request: NextRequest) {
  const { pathname } = request.nextUrl;

  // Define allowed static file extensions (security-first approach)
  const allowedStaticExtensions = [
    '.js',
    '.css',
    '.png',
    '.jpg',
    '.jpeg',
    '.gif',
    '.svg',
    '.ico',
    '.woff',
    '.woff2',
    '.ttf',
    '.eot',
    '.webp',
    '.avif',
    '.mp4',
    '.webm'
  ];

  // Define explicitly blocked file patterns (security critical)
  const blockedPatterns = [
    '.env',
    '.git',
    '.db',
    '.sqlite',
    '.log',
    '.bak',
    '.backup',
    '.config',
    '.ini',
    '.conf',
    '.key',
    '.pem',
    '.crt',
    '.p12',
    '.json',
    '.yaml',
    '.yml',
    '.xml',
    '.md',
    '.txt',
    '.csv'
  ];

  // Check for blocked file patterns first (highest security priority)
  const hasBlockedPattern = blockedPatterns.some(pattern =>
    pathname.toLowerCase().includes(pattern.toLowerCase())
  );

  if (hasBlockedPattern) {
    console.warn(`Blocked access to sensitive file: ${pathname}`);
    return NextResponse.json({ error: 'Access denied' }, { status: 403 });
  }

  // Skip middleware only for explicitly allowed static files and Next.js internals
  const isNextInternal = pathname.startsWith('/_next') || pathname.startsWith('/api/_next');
  const isFavicon = pathname === '/favicon.ico';
  const isAllowedStatic = allowedStaticExtensions.some(ext =>
    pathname.toLowerCase().endsWith(ext.toLowerCase())
  );

  if (isNextInternal || isFavicon || (isAllowedStatic && pathname.startsWith('/public/'))) {
    return NextResponse.next();
  }

  // Get the token to check authentication status
  const token = await getToken({ req: request, secret: process.env.NEXTAUTH_SECRET! });

  // Check if the route requires authentication
  const isProtectedRoute = protectedRoutes.some(route => pathname.startsWith(route));
  const isAdminRoute = adminRoutes.some(route => pathname.startsWith(route));
  const isProtectedApiRoute = protectedApiRoutes.some(route => pathname.startsWith(route));
  const isAdminApiRoute = adminApiRoutes.some(route => pathname.startsWith(route));
  const isPublicApiRoute = publicApiRoutes.some(route => pathname.startsWith(route));

  // Apply rate limiting for API routes and sensitive endpoints
  if (pathname.startsWith('/api/') || isAdminRoute || isAdminApiRoute) {
    // Use stricter rate limiting for admin routes
    const maxRequests = isAdminRoute || isAdminApiRoute ? 50 : 100;
    const windowMs = isAdminRoute || isAdminApiRoute ? 15 * 60 * 1000 : 15 * 60 * 1000;

    if (isRateLimited(request, windowMs, maxRequests)) {
      console.warn(`Rate limit exceeded for ${pathname}`, {
        ip: getClientIP(request),
        userAgent: request.headers.get('user-agent'),
        isAdmin: isAdminRoute || isAdminApiRoute
      });

      return NextResponse.json(
        { error: 'Too many requests. Please try again later.' },
        { status: 429 }
      );
    }
  }

  // Handle admin routes (require admin authentication)
  if (isAdminRoute) {
    if (!token) {
      const loginUrl = new URL('/login', request.url);
      const fullPath = pathname + (request.nextUrl.search || '');
      loginUrl.searchParams.set('callbackUrl', fullPath);
      return NextResponse.redirect(loginUrl);
    }

    // Check if user has admin role
    if (!token['role'] || token['role'] !== 'admin') {
      console.warn(`Non-admin user attempted to access admin route: ${pathname}`, {
        userId: token.sub,
        email: token.email,
        role: token['role']
      });
      return NextResponse.json({ error: 'Admin access required' }, { status: 403 });
    }
  }

  // Handle protected routes with enhanced loop prevention
  if (isProtectedRoute && !token) {
    // Enhanced redirect loop prevention
    const redirectCount = parseInt(request.headers.get('x-middleware-redirect-count') || '0');
    const maxRedirects = 2; // Reduced from 3 to prevent loops

    if (redirectCount >= maxRedirects) {
      console.warn(`Redirect loop prevented for ${pathname}, count: ${redirectCount}`);
      // Return 401 instead of continuing the loop
      return NextResponse.json(
        { error: 'Authentication required', redirectTo: '/login' },
        { status: 401 }
      );
    }

    // Avoid redirecting if already on login page
    if (pathname === '/login') {
      return NextResponse.next();
    }

    const loginUrl = new URL('/login', request.url);
    // Include query parameters in the redirect URL
    const fullPath = pathname + (request.nextUrl.search || '');
    loginUrl.searchParams.set('callbackUrl', fullPath);

    const response = NextResponse.redirect(loginUrl);
    // Track redirect count to prevent loops
    response.headers.set('x-middleware-redirect-count', (redirectCount + 1).toString());
    return response;
  }

  // Handle admin API routes (require admin authentication)
  if (isAdminApiRoute) {
    if (!token) {
      return NextResponse.json({ error: 'Authentication required' }, { status: 401 });
    }

    // Check if user has admin role
    if (!token['role'] || token['role'] !== 'admin') {
      console.warn(`Non-admin user attempted to access admin API: ${pathname}`, {
        userId: token.sub,
        email: token.email,
        role: token['role']
      });
      return NextResponse.json({ error: 'Admin access required' }, { status: 403 });
    }
  }

  // Handle protected API routes
  if (isProtectedApiRoute && !token && !isPublicApiRoute) {
    return NextResponse.json({ error: 'Authentication required' }, { status: 401 });
  }

  // Redirect authenticated users away from auth pages (with enhanced loop prevention)
  if (token && (pathname === '/login' || pathname === '/signup')) {
    // Enhanced redirect loop prevention
    const redirectCount = parseInt(request.headers.get('x-middleware-redirect-count') || '0');
    const maxRedirects = 2;

    if (redirectCount >= maxRedirects) {
      console.warn(`Auth redirect loop prevented for ${pathname}, count: ${redirectCount}`);
      // Allow access to auth page to prevent infinite loops
      const response = NextResponse.next();
      return addSecurityHeaders(response);
    }

    // Check if user is trying to access login with a callback URL
    const callbackUrl = request.nextUrl.searchParams.get('callbackUrl');

    // Validate callback URL to prevent open redirects
    let redirectTo = '/dashboard';
    if (callbackUrl &&
        callbackUrl !== '/login' &&
        callbackUrl !== '/signup' &&
        callbackUrl.startsWith('/') &&
        !callbackUrl.startsWith('//')) {
      redirectTo = callbackUrl;
    }

    const response = NextResponse.redirect(new URL(redirectTo, request.url));
    // Track redirect count
    response.headers.set('x-middleware-redirect-count', (redirectCount + 1).toString());
    return response;
  }

  const response = NextResponse.next();
  return addSecurityHeaders(response);
}

export const config = {
  matcher: [
    /*
     * Match all request paths except for the ones starting with:
     * - _next/static (static files)
     * - _next/image (image optimization files)
     * - favicon.ico (favicon file)
     * - public folder files
     * - api/auth (NextAuth.js routes)
     */
    '/((?!_next/static|_next/image|favicon.ico|public/|api/auth).*)'
  ]
};
